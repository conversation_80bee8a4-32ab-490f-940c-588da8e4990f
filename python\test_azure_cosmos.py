#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Azure Cosmos DB 測試腳本
展示如何使用修改後的 AzureCosmosManager 來處理不同年度的資料
"""

from AzureCosmos import AzureCosmosManager
from azure.cosmos import exceptions

def test_azure_cosmos():
    """測試 Azure Cosmos DB 功能"""
    
    # 連線資訊配置
    ENDPOINT = "https://yuntech-japan-nosql.documents.azure.com:443/"
    KEY = "****************************************************************************************"
    DATABASE_NAME = "Dashboard"

    # 建立 Cosmos DB 管理器
    cosmos_manager = AzureCosmosManager(ENDPOINT, KEY, DATABASE_NAME)

    try:
        print("🔗 連接到 Azure Cosmos DB...")
        if not cosmos_manager.connect_to_database():
            print("❌ 無法連接到資料庫")
            return

        print("\n📋 容器配置:")
        cosmos_manager.list_containers()

        print("\n🏗️  建立容器...")
        cosmos_manager.create_all_containers()

        # 測試匯入功能
        print("\n" + "="*60)
        print("📥 測試 CSV 資料匯入功能")
        print("="*60)
        
        # 只處理 UA_Lib 容器
        if "UA_Lib" in cosmos_manager.containers:
            print("\n🔄 匯入圖書館資料...")
            success = cosmos_manager.import_csv_data("UA_Lib")
            
            if success:
                print("✅ 資料匯入成功！")
                
                # 查詢所有年度
                print("\n📅 查詢所有年度:")
                cosmos_manager.list_all_years("UA_Lib")
                
                # 查詢特定年度的資料
                print("\n🔍 查詢年度 113 的資料:")
                cosmos_manager.query_data_by_year("UA_Lib", "113")
                
                print("\n🔍 查詢年度 112 的資料:")
                cosmos_manager.query_data_by_year("UA_Lib", "112")
                
                # 再次執行匯入，測試覆蓋功能
                print("\n" + "="*60)
                print("🔄 測試資料覆蓋功能 (再次匯入相同資料)")
                print("="*60)
                
                success_again = cosmos_manager.import_csv_data("UA_Lib")
                if success_again:
                    print("✅ 資料覆蓋成功！")
                    
                    # 再次查詢確認資料
                    print("\n📅 覆蓋後的所有年度:")
                    cosmos_manager.list_all_years("UA_Lib")
                
            else:
                print("❌ 資料匯入失敗")
        else:
            print("❌ UA_Lib 容器未建立")

        print("\n🎉 測試完成！")

    except exceptions.CosmosHttpResponseError as e:
        print(f"❌ Cosmos DB 操作錯誤: {e.message}")
    except Exception as e:
        print(f"❌ 發生未預期的錯誤: {e}")

def test_specific_year_query():
    """測試特定年度查詢功能"""
    
    # 連線資訊配置
    ENDPOINT = "https://yuntech-japan-nosql.documents.azure.com:443/"
    KEY = "****************************************************************************************"
    DATABASE_NAME = "Dashboard"

    cosmos_manager = AzureCosmosManager(ENDPOINT, KEY, DATABASE_NAME)

    try:
        if not cosmos_manager.connect_to_database():
            return

        # 建立容器
        cosmos_manager.create_container("UA_Lib")

        print("🔍 測試年度查詢功能:")
        print("-" * 40)
        
        # 測試查詢不同年度
        test_years = ["110", "111", "112", "113"]
        
        for year in test_years:
            print(f"\n📊 查詢年度 {year}:")
            cosmos_manager.query_data_by_year("UA_Lib", year)

    except Exception as e:
        print(f"❌ 測試查詢功能時發生錯誤: {e}")

if __name__ == "__main__":
    print("🚀 開始 Azure Cosmos DB 測試")
    print("="*60)
    
    # 執行主要測試
    test_azure_cosmos()
    
    print("\n" + "="*60)
    print("🔍 執行年度查詢測試")
    print("="*60)
    
    # 執行年度查詢測試
    test_specific_year_query()
