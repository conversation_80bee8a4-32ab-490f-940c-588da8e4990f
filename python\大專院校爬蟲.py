import requests
from bs4 import BeautifulSoup
import json
import os
import urllib.parse
import re
import concurrent.futures
import time
from tqdm import tqdm

# Step 1: 設定基本資料
base_url = 'https://udb.moe.edu.tw'
common_prefix = f'{base_url}/udata/StatCardList/University/'

# 學校網址物件陣列 (高科、雲科、台科、北科、)
universities = [
    {'name': '國立高雄科技大學', 'url': f'{common_prefix}00001476A5D4/0053/國立高雄科技大學'},
    {'name': '國立雲林科技大學', 'url': f'{common_prefix}000012CE61F9/0023/國立雲林科技大學'},
    {'name': '國立臺灣科技大學', 'url': f'{common_prefix}000012CE61ED/0022/國立臺灣科技大學'},
    {'name': '國立臺北科技大學', 'url': f'{common_prefix}000012CE61F3/0025/國立臺北科技大學'},
    # 可以加入更多學校
]

# 分類資料 ID 物件陣列
stat_categories = {
    '學生': {
        '在學學生數': 'StatStudent',
        '外國學生': 'StatForeignStudent',
        # 可以加入更多學生相關 ID
    },
    '教職': {
        '專任教師數': 'StatRegularTeacher',
        '外籍專任教師數': 'StatForeignTeacher',
        '日間生師比': 'StatUniversityTeacherStudentRatio',
        '編制外專任教師比率': 'SirdUniversityOutsideTeacherAndRate_New',
        # 可以加入更多教職相關 ID
    },
    '研究': {
        '學校承接各單位資助計畫經費': 'SirdIndustryCooperativeResearchProjectRatio',
        '產學合作計畫經費': 'SirdIndustryCooperativeResearchProject',
        # 可以加入更多研究相關 ID
    },
    '校務': {
        '圖書館統計': 'StatLibary',
        '學校提供一年級學生住宿之比率': 'StatAccommodationStatus',
        '學校開設全外語授課之院、系所、學位學程': 'SirdSchoolEstablishForeighnLanguageTeaching',
        # 可以加入更多校務相關 ID
    },
    '財務': {
        '國立學校可用資金': 'AccountingCashFlowsStatement',
        '學雜費收入占總收入之比率': 'AccountingTuitionsFeesIncome',
        # 可以加入更多財務相關 ID
    }
}

# 設定主要資料夾名稱
MAIN_FOLDER_NAME = "大專爬蟲"

# 設定最大同時下載數
MAX_WORKERS = 10
# 設定請求間隔 (秒)
REQUEST_DELAY = 0.5

def 獲取學校參數(url):
    """從學校網址中提取UniversityKey和UniversityId參數"""
    # 從URL中提取參數 (格式: common_prefix + Key + / + Id + / + Name)
    pattern = f'{common_prefix}([^/]+)/([^/]+)/(.+)'
    match = re.match(pattern, url)
    
    if match:
        return {
            'UniversityKey': match.group(1),
            'UniversityId': match.group(2),
            'UniversityName': match.group(3)
        }
    return None

def 獲取所有資料URLs(university_url, headers):
    """獲取單一學校所有資料的data-urls"""
    resp = requests.get(university_url, headers=headers)
    resp.encoding = 'utf-8'
    soup = BeautifulSoup(resp.text, 'html.parser')
    
    data_urls = {}
    
    # 針對每個分類的每個資料ID，嘗試獲取data-url
    for category, items in stat_categories.items():
        data_urls[category] = {}
        for name, stat_id in items.items():
            try:
                stat_div = soup.find(id=stat_id)
                if stat_div and 'data-url' in stat_div.attrs:
                    data_urls[category][name] = {
                        'stat_id': stat_id,
                        'data_url': stat_div['data-url']
                    }
                else:
                    print(f"警告: 找不到 {stat_id} 的 data-url")
            except Exception as e:
                print(f"獲取 {stat_id} data-url 時發生錯誤: {str(e)}")
    
    return data_urls

def 獲取卡片標題(full_url, headers, retry_count=0, max_retries=3):
    """從AJAX頁面獲取卡片標題和年份"""
    time.sleep(REQUEST_DELAY)  # 避免請求過於頻繁
    try:
        ajax_resp = requests.get(full_url, headers=headers)
        ajax_resp.encoding = 'utf-8'
        ajax_soup = BeautifulSoup(ajax_resp.text, 'html.parser')
        
        title_tag = ajax_soup.select_one('.card-title strong')
        if title_tag:
            card_title = title_tag.text.strip()
            year = card_title[:3]  # 前三字為年份
            return card_title, year
        else:
            # 找不到標題標籤，可能是網頁結構問題
            if retry_count < max_retries:
                retry_delay = REQUEST_DELAY * (2 ** retry_count)
                print(f"找不到卡片標題，{retry_delay}秒後進行第{retry_count+1}次重試...")
                time.sleep(retry_delay)
                return 獲取卡片標題(full_url, headers, retry_count + 1, max_retries)
            else:
                # 多次重試後仍無法找到標題，使用預設值
                card_title = '未知卡片標題'
                year = ''
                return card_title, year
    except requests.exceptions.RequestException as e:
        if retry_count < max_retries:
            retry_delay = REQUEST_DELAY * (2 ** retry_count)
            error_type = type(e).__name__
            print(f"獲取卡片標題時發生 {error_type} 錯誤，{retry_delay}秒後進行第{retry_count+1}次重試...")
            time.sleep(retry_delay)
            return 獲取卡片標題(full_url, headers, retry_count + 1, max_retries)
        else:
            raise Exception(f"獲取卡片標題失敗，已重試{retry_count}次: {str(e)}")

def 組合下載參數(year, university_info, stat_id):
    """組合API下載所需的參數"""
    return {
        "FileType": "xlsx",
        "Parameter": json.dumps({
            "Year": [year],
            "AreaId": "N",
            "AreaName": "N",
            "UniversityKey": university_info['UniversityKey'],
            "UniversityId": university_info['UniversityId'],
            "UniversityList": None,
            "UniversityName": university_info['UniversityName'],
            "IscedId": "N",
            "IscedName": "N",
            "EduSystemId": "N",
            "EduSystemName": "N",
            "PageNumber": 1,
            "ExportType": None,
            "AuditStatus": None
        })
    }

def 發送下載請求(download_api, payload, headers, retry_count=0, max_retries=3):
    """發送下載請求並獲取JSON回應，如果失敗會重試"""
    time.sleep(REQUEST_DELAY)  # 避免請求過於頻繁
    try:
        download_resp = requests.post(download_api, data=payload, headers=headers)
        # 檢查HTTP狀態碼
        download_resp.raise_for_status()
        result_json = download_resp.json()
        return result_json
    except json.JSONDecodeError as e:
        # JSON解析錯誤，通常是因為伺服器返回的不是有效的JSON
        if retry_count < max_retries:
            # 重試次數未達上限，等待更長時間後重試
            retry_delay = REQUEST_DELAY * (2 ** retry_count)  # 指數退避策略
            print(f"發生JSON解析錯誤，{retry_delay}秒後重試第{retry_count+1}次...")
            time.sleep(retry_delay)
            return 發送下載請求(download_api, payload, headers, retry_count + 1, max_retries)
        else:
            # 重試次數已達上限，拋出異常
            raise Exception(f"下載請求失敗，JSON解析錯誤: {str(e)}，已重試{retry_count}次")
    except requests.exceptions.RequestException as e:
        # HTTP請求錯誤
        if retry_count < max_retries:
            retry_delay = REQUEST_DELAY * (2 ** retry_count)  # 指數退避策略
            error_type = type(e).__name__
            print(f"發生HTTP請求錯誤({error_type})，{retry_delay}秒後重試第{retry_count+1}次...")
            time.sleep(retry_delay)
            return 發送下載請求(download_api, payload, headers, retry_count + 1, max_retries)
        else:
            # 重試次數已達上限，拋出異常
            raise Exception(f"下載請求失敗，HTTP錯誤: {str(e)}，已重試{retry_count}次")

def 獲取下載連結(base_url, file_handle, file_title, stat_id):
    """組合最終下載連結"""
    return f'{base_url}/udata/{stat_id}/Download/?fileGuid={file_handle}&filename={urllib.parse.quote(file_title)}'

def 下載檔案(download_url, headers, output_path, retry_count=0, max_retries=3):
    """下載並保存檔案"""
    time.sleep(REQUEST_DELAY)  # 避免請求過於頻繁
    try:
        file_resp = requests.get(download_url, headers=headers)
        file_resp.raise_for_status()
        
        with open(output_path, 'wb') as f:
            f.write(file_resp.content)
        
        return output_path
    except (requests.exceptions.RequestException, IOError) as e:
        if retry_count < max_retries:
            retry_delay = REQUEST_DELAY * (2 ** retry_count)  # 指數退避策略
            error_type = type(e).__name__
            print(f"下載檔案時發生 {error_type} 錯誤，{retry_delay}秒後進行第{retry_count+1}次重試...")
            time.sleep(retry_delay)
            return 下載檔案(download_url, headers, output_path, retry_count + 1, max_retries)
        else:
            raise Exception(f"下載檔案失敗，已重試{retry_count}次: {str(e)}")

def 處理單一下載任務(task_info, retry_count=0, max_retries=3):
    """處理單一下載任務，用於多執行緒"""
    university_name = task_info['university_name']
    category = task_info['category']
    name = task_info['name']
    stat_id = task_info['stat_id']
    data_url = task_info['data_url']
    university_info = task_info['university_info']
    headers = task_info['headers']
    
    try:
        # 1. 獲取完整資料URL
        referer = base_url + data_url
        
        # 2. 獲取卡片標題和年份
        card_title, year = 獲取卡片標題(referer, headers)
        
        # 3. 組合下載API參數
        download_api = f'{base_url}/udata/DetailReport/{stat_id}/Export/xlsx/Parameter'
        payload = 組合下載參數(year, university_info, stat_id)
        
        # 4. 設定下載標頭
        download_headers = {
            'User-Agent': 'Mozilla/5.0',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Referer': referer,
            'Origin': base_url,
            'X-Requested-With': 'XMLHttpRequest'
        }
        
        # 5. 發送下載請求 (現在包含重試機制)
        result_json = 發送下載請求(download_api, payload, download_headers)
        
        # 6. 獲取下載連結
        file_handle = result_json["handle"]
        file_title = result_json["fileName"]
        download_url = 獲取下載連結(base_url, file_handle, file_title, stat_id)
        
        # 7. 下載檔案 (更改儲存路徑結構)
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        main_folder = os.path.join(desktop_path, MAIN_FOLDER_NAME)
        category_folder = os.path.join(main_folder, category)
        os.makedirs(category_folder, exist_ok=True)
        
        # 檔名包含學校名稱，但不以學校作為目錄結構
        output_filename = f"{university_name}-{card_title}.xlsx" #f"{university_name}-{name}-{card_title}.xlsx"
        output_path = os.path.join(category_folder, output_filename)
        
        final_path = 下載檔案(download_url, headers, output_path)
        
        return {
            "university": university_name,
            "category": category,
            "name": name,
            "file_path": final_path,
            "status": "success"
        }
    except (json.JSONDecodeError, requests.exceptions.RequestException, Exception) as error:
        # 檢查是否可以重試
        if retry_count < max_retries:
            retry_delay = REQUEST_DELAY * (2 ** retry_count)  # 指數退避策略
            error_type = type(error).__name__
            print(f"下載 {university_name} 的 {category}/{name} 時發生 {error_type} 錯誤，{retry_delay}秒後進行第{retry_count+1}次重試...")
            time.sleep(retry_delay)
            return 處理單一下載任務(task_info, retry_count + 1, max_retries)
        
        # 超過最大重試次數，回報失敗
        error_msg = f"下載 {university_name} 的 {category}/{name} 時發生錯誤: {str(error)}"
        print(f"下載失敗 (已重試{retry_count}次): {error_msg}")
        
        # 根據錯誤類型返回不同的錯誤信息
        if isinstance(error, json.JSONDecodeError):
            return {
                "university": university_name,
                "category": category,
                "name": name,
                "status": "failed",
                "error": error_msg,
                "error_type": "json_decode",
                "retry_count": retry_count
            }
        else:
            return {
                "university": university_name,
                "category": category,
                "name": name,
                "status": "failed",
                "error": str(error),
                "retry_count": retry_count
            }

def 主程序():
    """主要下載程序"""
    start_time = time.time()
    
    # 設定請求標頭
    headers = {
        'User-Agent': 'Mozilla/5.0',
    }
    
    download_results = []
    failed_results = []  # 新增失敗項目列表
    download_tasks = []
    
    # 創建主資料夾
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
    main_folder = os.path.join(desktop_path, MAIN_FOLDER_NAME)
    os.makedirs(main_folder, exist_ok=True)
    
    # 創建分類資料夾
    for category in stat_categories.keys():
        category_folder = os.path.join(main_folder, category)
        os.makedirs(category_folder, exist_ok=True)
        print(f"已創建資料夾: {category_folder}")
    
    # 準備所有下載任務
    for university in universities:
        try:
            university_name = university['name']
            university_url = university['url']
            university_info = 獲取學校參數(university_url)
            
            if not university_info:
                print(f"無法從網址解析出學校資訊: {university_url}")
                continue
            
            print(f"準備 {university_name} 的下載任務...")
            
            # 獲取所有資料URLs
            all_data_urls = 獲取所有資料URLs(university_url, headers)
            
            # 建立下載任務
            for category, items in all_data_urls.items():
                for name, url_info in items.items():
                    task = {
                        'university_name': university_name,
                        'category': category,
                        'name': name,
                        'stat_id': url_info['stat_id'],
                        'data_url': url_info['data_url'],
                        'university_info': university_info,
                        'headers': headers.copy()  # 複製 headers 避免共享引用
                    }
                    download_tasks.append(task)
            
        except Exception as error:
            print(f"處理 {university.get('name', '未知學校')} 時發生錯誤: {str(error)}")
    
    # 顯示任務總數
    total_tasks = len(download_tasks)
    print(f"總共 {total_tasks} 個下載任務準備就緒")
    
    # 多執行緒並行下載
    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        # 使用 tqdm 顯示進度條
        results = list(tqdm(
            executor.map(處理單一下載任務, download_tasks),
            total=total_tasks,
            desc="批量下載進度"
        ))
        
        # 收集結果
        for result in results:
            if result:
                if result.get('status') == 'success':
                    download_results.append(result)
                else:
                    failed_results.append(result)
    
    # 顯示統計資訊
    success_count = len(download_results)
    failed_count = len(failed_results)
    
    print(f"\n下載統計:")
    print(f"總共任務數: {total_tasks}")
    print(f"成功下載數: {success_count}")
    print(f"失敗下載數: {failed_count}")
    
    # 顯示失敗項目詳細資訊
    if failed_count > 0:
        print("\n失敗下載項目詳細資訊:")
        for i, failed in enumerate(failed_results, 1):
            print(f"{i}. {failed['university']} 的 {failed['category']}/{failed['name']}")
            print(f"   錯誤原因: {failed.get('error', '未知錯誤')}")
            print(f"   已嘗試次數: {failed.get('retry_count', 0)}")
            print()
    
    # 計算執行時間
    end_time = time.time()
    execution_time = end_time - start_time
    print(f"總執行時間: {execution_time:.2f} 秒")
    print(f"檔案已儲存至桌面的 '{MAIN_FOLDER_NAME}' 資料夾")
    
    return {
        "success": download_results,
        "failed": failed_results
    }

if __name__ == "__main__":
    主程序()
