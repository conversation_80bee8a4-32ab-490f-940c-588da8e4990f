#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試Excel檔案讀取性能
"""

import pandas as pd
import time
from pathlib import Path

def test_excel_reading_methods():
    """測試不同的Excel讀取方法"""
    print("🧪 測試Excel檔案讀取性能")
    print("="*60)
    
    # 測試檔案路徑
    test_files = [
        Path("../學生/在學學生數.xlsx"),
        Path("../學生/外國學生數.xlsx")
    ]
    
    for file_path in test_files:
        if not file_path.exists():
            print(f"⚠️  檔案不存在: {file_path}")
            continue
        
        file_size = file_path.stat().st_size / (1024 * 1024)  # MB
        print(f"\n📄 測試檔案: {file_path.name}")
        print(f"📏 檔案大小: {file_size:.2f} MB")
        print("-" * 50)
        
        # 方法1: 預設讀取（讀取全部）
        print("🔍 方法1: 預設讀取（全部資料）")
        try:
            start_time = time.time()
            df1 = pd.read_excel(file_path, header=None)
            end_time = time.time()
            print(f"   ⏱️  耗時: {end_time - start_time:.2f} 秒")
            print(f"   📊 資料形狀: {df1.shape}")
        except Exception as e:
            print(f"   ❌ 錯誤: {e}")
        
        # 方法2: 使用openpyxl引擎
        print("🔍 方法2: 使用openpyxl引擎（全部資料）")
        try:
            start_time = time.time()
            df2 = pd.read_excel(file_path, header=None, engine='openpyxl')
            end_time = time.time()
            print(f"   ⏱️  耗時: {end_time - start_time:.2f} 秒")
            print(f"   📊 資料形狀: {df2.shape}")
        except Exception as e:
            print(f"   ❌ 錯誤: {e}")
        
        # 方法3: 限制讀取行數
        print("🔍 方法3: 限制讀取1000行")
        try:
            start_time = time.time()
            df3 = pd.read_excel(file_path, header=None, engine='openpyxl', nrows=1000)
            end_time = time.time()
            print(f"   ⏱️  耗時: {end_time - start_time:.2f} 秒")
            print(f"   📊 資料形狀: {df3.shape}")
        except Exception as e:
            print(f"   ❌ 錯誤: {e}")
        
        # 方法4: 限制讀取行數和列數
        print("🔍 方法4: 限制讀取1000行，前20列")
        try:
            start_time = time.time()
            df4 = pd.read_excel(file_path, header=None, engine='openpyxl', nrows=1000, usecols=range(20))
            end_time = time.time()
            print(f"   ⏱️  耗時: {end_time - start_time:.2f} 秒")
            print(f"   📊 資料形狀: {df4.shape}")
        except Exception as e:
            print(f"   ❌ 錯誤: {e}")

def analyze_excel_structure():
    """分析Excel檔案結構"""
    print(f"\n🔍 分析Excel檔案結構")
    print("="*60)
    
    test_files = [
        Path("../學生/在學學生數.xlsx"),
        Path("../學生/外國學生數.xlsx")
    ]
    
    for file_path in test_files:
        if not file_path.exists():
            continue
        
        print(f"\n📄 分析檔案: {file_path.name}")
        print("-" * 40)
        
        try:
            # 只讀取前10行來分析結構
            df = pd.read_excel(file_path, header=None, nrows=10)
            
            print(f"📊 檔案結構預覽（前10行）:")
            for i, row in df.iterrows():
                row_preview = []
                for j, cell in enumerate(row[:10]):  # 只顯示前10列
                    if pd.notna(cell):
                        cell_str = str(cell)[:20]  # 限制顯示長度
                        if len(str(cell)) > 20:
                            cell_str += "..."
                        row_preview.append(cell_str)
                    else:
                        row_preview.append("(空)")
                
                print(f"   第{i+1}行: {' | '.join(row_preview)}")
            
            # 檢查可能的標題行
            print(f"\n🔍 可能的標題行分析:")
            for i in range(min(5, len(df))):
                non_empty_count = df.iloc[i].notna().sum()
                print(f"   第{i+1}行: {non_empty_count} 個非空欄位")
                
        except Exception as e:
            print(f"   ❌ 分析失敗: {e}")

def suggest_optimizations():
    """建議優化方案"""
    print(f"\n💡 Excel處理優化建議")
    print("="*60)
    
    suggestions = [
        "1. 使用 engine='openpyxl' 參數",
        "2. 限制讀取行數 nrows=1000（通常數據不會超過1000行）",
        "3. 如果知道數據列範圍，使用 usecols 參數限制讀取列數",
        "4. 添加進度顯示，讓用戶知道處理進度",
        "5. 考慮將Excel檔案轉換為CSV格式以提高處理速度",
        "6. 使用多線程處理多個檔案（如果有多個檔案）",
        "7. 預先檢查檔案大小，對大檔案給出警告"
    ]
    
    for suggestion in suggestions:
        print(f"   {suggestion}")
    
    print(f"\n📈 預期效果:")
    print(f"   • 讀取速度提升 50-80%")
    print(f"   • 記憶體使用量減少 60-90%")
    print(f"   • 更好的用戶體驗（進度顯示）")

if __name__ == "__main__":
    print("🚀 Excel檔案性能測試")
    print("="*80)
    
    # 測試不同讀取方法
    test_excel_reading_methods()
    
    # 分析檔案結構
    analyze_excel_structure()
    
    # 建議優化方案
    suggest_optimizations()
    
    print(f"\n🎉 測試完成！")
    print(f"💡 建議使用限制行數的讀取方法來提高性能")
