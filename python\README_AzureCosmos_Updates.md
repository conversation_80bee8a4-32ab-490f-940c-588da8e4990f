# Azure Cosmos DB 管理器更新說明

## 概述

已成功修改 `AzureCosmos.py` 文件，實現了按年度管理資料的功能。現在系統可以：

1. **自動按年度分組處理 CSV 資料**
2. **檢查 Azure Cosmos DB 中是否已存在相同年度的資料**
3. **覆蓋現有年度資料或新增不同年度資料**

## 主要修改內容

### 1. 修改 `import_csv_data` 方法

**原功能：** 將整個 CSV 文件作為一筆資料匯入

**新功能：**
- 按年度欄位（如：學年度）自動分組資料
- 每個年度創建一個獨立的 item
- 自動檢查是否已存在相同年度的資料
- 如果存在則覆蓋，如果不存在則新增

### 2. 新增 `check_existing_year_data` 方法

**功能：** 檢查指定年度的資料是否已存在於 Azure Cosmos DB 中

**參數：**
- `container`: Cosmos DB 容器
- `year`: 年度字串

**返回：** 如果存在則返回該項目，否則返回 None

### 3. 新增 `query_data_by_year` 方法

**功能：** 查詢指定年度的資料

**參數：**
- `container_key`: 容器鍵值
- `year`: 年度（可選，如果為 None 則查詢所有年度）

### 4. 新增 `list_all_years` 方法

**功能：** 列出容器中所有的年度

**參數：**
- `container_key`: 容器鍵值

## 資料結構

### CSV 文件結構
```csv
學年度,設立別,學校類別,學校統計處代碼,學校名稱,...
110,公立,技專校院,0023,國立雲林科技大學,...
111,公立,技專校院,0023,國立雲林科技大學,...
112,公立,技專校院,0023,國立雲林科技大學,...
113,公立,技專校院,0023,國立雲林科技大學,...
```

### Azure Cosmos DB 資料結構
```json
{
  "id": "unique-uuid",
  "年度": "113",
  "data": [
    {
      "學年度": "113",
      "設立別": "公立",
      "學校類別": "技專校院",
      "學校統計處代碼": "0023",
      "學校名稱": "國立雲林科技大學",
      ...
    },
    ...
  ]
}
```

## 使用方式

### 基本使用
```python
from AzureCosmos import AzureCosmosManager

# 建立管理器
cosmos_manager = AzureCosmosManager(ENDPOINT, KEY, DATABASE_NAME)

# 連接資料庫
cosmos_manager.connect_to_database()

# 建立容器
cosmos_manager.create_container("UA_Lib")

# 匯入 CSV 資料（自動按年度處理）
cosmos_manager.import_csv_data("UA_Lib")
```

### 查詢功能
```python
# 列出所有年度
cosmos_manager.list_all_years("UA_Lib")

# 查詢特定年度資料
cosmos_manager.query_data_by_year("UA_Lib", "113")

# 查詢所有年度資料
cosmos_manager.query_data_by_year("UA_Lib")
```

## 執行流程

1. **讀取 CSV 文件**
2. **按年度欄位分組資料**
3. **對每個年度：**
   - 檢查 Azure Cosmos DB 中是否已存在該年度資料
   - 如果存在：使用 `upsert_item` 覆蓋現有資料
   - 如果不存在：使用 `create_item` 新增資料
4. **顯示處理結果**

## 測試

執行測試腳本：
```bash
python test_azure_cosmos.py
```

測試腳本會：
1. 匯入圖書館歷年版 CSV 資料
2. 查詢所有年度
3. 查詢特定年度資料
4. 再次匯入相同資料測試覆蓋功能

## 配置說明

在 `container_configs` 中的 `UA_Lib` 配置：
```python
"UA_Lib": {
    "name": "UA_Lib",
    "partition_key": "/年度",
    "csv_file": "../校務/已整合-圖書館歷年版.csv",
    "year_column": "學年度",  # CSV 中的年度欄位名稱
}
```

## 注意事項

1. **年度欄位名稱**：確保 `year_column` 設定正確對應 CSV 中的年度欄位
2. **分割鍵**：使用 `/年度` 作為分割鍵，提高查詢效能
3. **資料覆蓋**：相同年度的資料會被完全覆蓋，請確保這是預期行為
4. **錯誤處理**：包含完整的錯誤處理和狀態回報

## 優勢

1. **自動化處理**：無需手動分割年度資料
2. **智能更新**：自動判斷是否需要覆蓋或新增
3. **高效查詢**：按年度分割，提高查詢效能
4. **完整日誌**：詳細的處理過程和結果回報
5. **向後兼容**：保留原有方法，不影響現有功能
