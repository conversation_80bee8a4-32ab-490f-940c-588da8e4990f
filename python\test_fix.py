#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試修正後的XLSX下載功能
"""

import requests
from bs4 import BeautifulSoup

# 設定基本資料
base_url = 'https://udb.moe.edu.tw'

def 獲取分類頁面內容(category_url, headers):
    """獲取分類頁面的HTML內容"""
    try:
        response = requests.get(category_url, headers=headers)
        response.encoding = 'utf-8'
        soup = BeautifulSoup(response.text, 'html.parser')
        return soup
    except Exception as e:
        print(f"獲取頁面內容時發生錯誤: {str(e)}")
        return None

def 尋找XLSX下載連結(soup, target_text):
    """在頁面中尋找指定文字的XLSX下載連結"""
    try:
        # 尋找包含目標文字的<a>標籤
        target_links = soup.find_all('a', string=lambda text: text and target_text in text.strip())
        
        if not target_links:
            print(f"找不到包含 '{target_text}' 的連結")
            return None
        
        # 找到目標連結後，尋找同一行的XLSX下載按鈕
        for link in target_links:
            # 找到包含此連結的<tr>元素
            tr_element = link.find_parent('tr')
            if tr_element:
                # 在這個<tr>中尋找XLSX下載連結
                xlsx_link = tr_element.find('a', href=lambda href: href and '.xlsx' in href)
                if xlsx_link:
                    xlsx_url = xlsx_link.get('href')
                    # 如果是相對路徑，轉換為絕對路徑
                    if xlsx_url.startswith('/'):
                        xlsx_url = base_url + xlsx_url
                    return xlsx_url
        
        print(f"找到 '{target_text}' 連結，但找不到對應的XLSX下載連結")
        return None
        
    except Exception as e:
        print(f"尋找XLSX下載連結時發生錯誤: {str(e)}")
        return None

def test_xlsx_download():
    """測試XLSX下載功能"""
    print("🧪 測試修正後的XLSX下載功能")
    print("="*50)
    
    # 測試參數
    category_url = 'https://udb.moe.edu.tw/udata/DetailReportList/學生類'
    target_text = '學1-1.正式學籍在學學生人數-以「系(所)」統計'
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    print(f"📂 測試頁面: {category_url}")
    print(f"🎯 尋找目標: {target_text}")
    
    # 1. 獲取頁面內容
    soup = 獲取分類頁面內容(category_url, headers)
    if not soup:
        print("❌ 無法獲取頁面內容")
        return
    
    print("✅ 成功獲取頁面內容")
    
    # 2. 尋找XLSX下載連結
    xlsx_url = 尋找XLSX下載連結(soup, target_text)
    if xlsx_url:
        print(f"✅ 成功找到XLSX下載連結: {xlsx_url}")
        return xlsx_url
    else:
        print("❌ 找不到XLSX下載連結")
        return None

if __name__ == "__main__":
    result = test_xlsx_download()
    if result:
        print(f"\n🎉 測試成功！XLSX下載連結: {result}")
    else:
        print(f"\n❌ 測試失敗！")
