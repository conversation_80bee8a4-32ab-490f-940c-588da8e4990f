#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試編制外專任教師比率的搜尋功能
"""

import requests
from bs4 import BeautifulSoup

# 設定基本資料
base_url = 'https://udb.moe.edu.tw'

def 獲取分類頁面內容(category_url, headers):
    """獲取分類頁面的HTML內容"""
    try:
        response = requests.get(category_url, headers=headers)
        response.encoding = 'utf-8'
        soup = BeautifulSoup(response.text, 'html.parser')
        return soup
    except Exception as e:
        print(f"獲取頁面內容時發生錯誤: {str(e)}")
        return None

def 測試編制外教師搜尋():
    """測試編制外專任教師比率的搜尋"""
    print("🧪 測試編制外專任教師比率搜尋")
    print("="*60)
    
    category_url = 'https://udb.moe.edu.tw/udata/DetailReportList/教職類'
    target_text = '教11.編制外專任教師數及其比率-以「校」統計(112學年度起)'
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    print(f"📂 測試頁面: {category_url}")
    print(f"🎯 尋找目標: {target_text}")
    
    # 獲取頁面內容
    soup = 獲取分類頁面內容(category_url, headers)
    if not soup:
        print("❌ 無法獲取頁面內容")
        return
    
    print("✅ 成功獲取頁面內容")
    
    # 測試不同的搜尋方法
    print(f"\n🔍 測試不同的搜尋方法:")
    
    # 方法1: 精確文字匹配
    method1 = soup.find_all('a', string=target_text)
    print(f"方法1 (精確匹配): {len(method1)} 個結果")
    
    # 方法2: 包含匹配
    method2 = soup.find_all('a', string=lambda text: text and target_text in text)
    print(f"方法2 (包含匹配): {len(method2)} 個結果")
    
    # 方法3: get_text()匹配
    method3 = soup.find_all('a', lambda tag: tag.get_text() and target_text in tag.get_text())
    print(f"方法3 (get_text匹配): {len(method3)} 個結果")
    
    # 方法4: title屬性匹配
    method4 = soup.find_all('a', title=lambda title: title and target_text in title)
    print(f"方法4 (title匹配): {len(method4)} 個結果")
    
    # 方法5: 部分匹配（去除括號）
    main_part = target_text.split('(')[0].strip()
    method5 = soup.find_all('a', lambda tag: tag.get_text() and main_part in tag.get_text())
    print(f"方法5 (部分匹配 '{main_part}'): {len(method5)} 個結果")
    
    # 方法6: 尋找包含"編制外"的所有連結
    method6 = soup.find_all('a', lambda tag: tag.get_text() and '編制外' in tag.get_text())
    print(f"方法6 (包含'編制外'): {len(method6)} 個結果")
    
    # 顯示找到的結果
    if method6:
        print(f"\n📋 包含'編制外'的連結:")
        for i, link in enumerate(method6, 1):
            print(f"  {i}. text: {repr(link.get_text())}")
            print(f"     title: {link.get('title', '無title')}")
            print(f"     href: {link.get('href', '無href')}")
            print()
    
    # 測試最有可能成功的方法
    if method5:
        print(f"✅ 使用方法5找到匹配，測試XLSX下載:")
        link = method5[0]
        tr_element = link.find_parent('tr')
        if tr_element:
            xlsx_link = tr_element.find('a', href=lambda href: href and '.xlsx' in href)
            if xlsx_link:
                xlsx_url = xlsx_link.get('href')
                if xlsx_url.startswith('/'):
                    xlsx_url = base_url + xlsx_url
                print(f"✅ 找到XLSX下載連結: {xlsx_url}")
                return xlsx_url
            else:
                print("❌ 找不到XLSX下載連結")
        else:
            print("❌ 找不到父級<tr>元素")
    
    return None

if __name__ == "__main__":
    result = 測試編制外教師搜尋()
    if result:
        print(f"\n🎉 測試成功！")
    else:
        print(f"\n❌ 測試失敗！")
