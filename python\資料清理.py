import os
import pandas as pd
from pathlib import Path
import datetime
import re
import time

def 獲取當前民國年():
    """
    獲取當前的民國年

    Returns:
        當前民國年（整數）
    """
    current_year = datetime.datetime.now().year
    return current_year - 1911

def 是否為有效學年度(year_value):
    """
    檢查學年度是否在有效範圍內（110年到當前民國年）

    Args:
        year_value: 學年度值

    Returns:
        布林值，True表示在有效範圍內
    """
    if pd.isna(year_value):
        return False

    try:
        year = int(str(year_value).strip())
        current_minguo_year = 獲取當前民國年()
        return 110 <= year <= current_minguo_year
    except (ValueError, TypeError):
        return False

def 是否為目標學校(school_name):
    """
    檢查是否為目標的四所學校

    Args:
        school_name: 學校名稱

    Returns:
        布林值，True表示是目標學校
    """
    if pd.isna(school_name):
        return False

    target_schools = [
        '國立雲林科技大學',
        '國立高雄科技大學',
        '國立臺灣科技大學',
        '國立臺北科技大學'
    ]

    school_str = str(school_name).strip()
    return school_str in target_schools

def 移除千分位逗號(value):
    """
    移除數值中的千分位逗號

    Args:
        value: 原始值

    Returns:
        移除千分位逗號後的值
    """
    if pd.isna(value):
        return value

    # 轉換為字串
    value_str = str(value).strip()

    # 使用正則表達式移除千分位逗號
    # 只移除數字中的逗號，保留其他格式
    if re.match(r'^[\d,]+\.?\d*$', value_str):
        return value_str.replace(',', '')

    return value

def 轉換學校名稱(name):
    """
    將完整學校名稱轉換為簡稱

    Args:
        name: 原始學校名稱

    Returns:
        轉換後的學校名稱
    """
    if pd.isna(name):
        return name

    name_str = str(name).strip()

    # 學校名稱對應表
    name_mapping = {
        '國立雲林科技大學': '雲科大',
        '國立高雄科技大學': '高科大',
        '國立臺灣科技大學': '臺科大',
        '國立臺北科技大學': '北科大'
    }

    return name_mapping.get(name_str, name_str)

def 尋找資料夾(target_dir, folder_keywords=None):
    """
    在指定目錄下尋找符合關鍵字的資料夾

    Args:
        target_dir: 要搜尋的起始目錄
        folder_keywords: 包含資料夾關鍵字的列表，如果為None則返回所有資料夾

    Returns:
        找到的資料夾路徑列表
    """
    found_folders = []

    try:
        for item in os.listdir(target_dir):
            full_path = Path(target_dir) / item

            if full_path.is_dir():
                # 如果沒有指定關鍵字，或者資料夾名符合任一關鍵字
                if folder_keywords is None or any(keyword in item for keyword in folder_keywords):
                    found_folders.append(full_path)
    except Exception as e:
        print(f"搜尋資料夾時發生錯誤: {e}")

    return found_folders

def 尋找檔案(target_dir, file_keywords=None, extensions=None):
    """
    在指定目錄下尋找符合關鍵字和副檔名的檔案

    Args:
        target_dir: 要搜尋的目錄
        file_keywords: 包含檔案名關鍵字的列表，如果為None則不篩選關鍵字
        extensions: 包含副檔名的列表(如['.xlsx', '.xls'])，如果為None則不篩選副檔名

    Returns:
        找到的檔案路徑列表
    """
    found_files = []

    try:
        for item in os.listdir(target_dir):
            full_path = Path(target_dir) / item

            if full_path.is_file():
                # 忽略已整合的檔案
                if "已整合-" in item:
                    continue

                # 忽略 Excel 臨時檔案（以 ~$ 開頭）
                if item.startswith("~$"):
                    continue

                # 忽略隱藏檔案（以 . 開頭）
                if item.startswith("."):
                    continue

                # 檢查副檔名
                ext_match = extensions is None or full_path.suffix in extensions

                # 檢查關鍵字
                keyword_match = file_keywords is None or any(keyword in item for keyword in file_keywords)

                if ext_match and keyword_match:
                    found_files.append(full_path)
    except Exception as e:
        print(f"搜尋檔案時發生錯誤: {e}")

    return found_files

def 處理Excel檔案(file_paths, title_row_index, data_row_index, data_end_row_index=None,
                 custom_processor=None, filter_data=True):
    """
    處理Excel檔案並整合數據

    Args:
        file_paths: 要處理的Excel檔案路徑列表
        title_row_index: 標題所在的列索引 (從0開始)，默認為第三列 (索引2)
        data_row_index: 資料開始的列索引 (從0開始)，默認為第四列 (索引3)
        data_end_row_index: 資料結束的列索引 (從0開始)，如果為None則處理到"統計說明"或檔案結尾
        custom_processor: 自定義處理函數，格式為 function(df, title_row, data_row)，用於執行複雜的數據處理
        filter_data: 是否進行數據過濾（學年度和學校篩選），默認為True

    Returns:
        整合後的數據字典
    """
    all_data = {}
    processed_files = []  # 用於記錄已處理的檔案名稱

    total_files = len(file_paths)
    print(f"📊 開始處理 {total_files} 個Excel檔案...")

    for i, file_path in enumerate(file_paths, 1):
        processed_files.append(file_path.name)
        try:
            print(f"📖 正在讀取Excel檔案 ({i}/{total_files}): {file_path.name}")

            # 獲取檔案大小
            file_size = file_path.stat().st_size / (1024 * 1024)  # MB
            print(f"   📏 檔案大小: {file_size:.2f} MB")

            start_time = time.time()

            # 使用openpyxl引擎優化讀取速度，並且只讀取前1000行（通常數據不會超過這個範圍）
            # 如果檔案很大，給出提示
            if file_size > 5:  # 大於5MB
                print(f"   ⚠️  檔案較大，讀取可能需要較長時間...")

            df = pd.read_excel(file_path, header=None, engine='openpyxl', nrows=1000)

            read_time = time.time() - start_time
            print(f"   ⏱️  讀取耗時: {read_time:.2f} 秒")

            # 檢查檔案是否有足夠的列數
            if len(df) <= title_row_index:
                print(f"警告: {file_path.name} 行數不足，無法提取標題 (需要第 {title_row_index+1} 列)")
                continue

            if len(df) <= data_row_index:
                print(f"警告: {file_path.name} 行數不足，無法提取數據值 (需要第 {data_row_index+1} 列)")
                continue

            # 提取標題
            title_row = df.iloc[title_row_index].tolist()

            # 確定資料結束的列索引（尋找包含"統計說明"的列）
            # 每個檔案都需要重新尋找統計說明的位置
            current_end_row_index = data_end_row_index
            if current_end_row_index is None:
                current_end_row_index = len(df)  # 默認到檔案末尾
                for row_idx in range(data_row_index, len(df)):
                    row_values = df.iloc[row_idx].astype(str).tolist()
                    if any("統計說明" in str(val) for val in row_values):
                        current_end_row_index = row_idx
                        break

            # 使用自定義處理器處理數據 (如果提供)
            if custom_processor is not None:
                for row_idx in range(data_row_index, current_end_row_index):
                    data_row = df.iloc[row_idx].tolist()
                    processed_data = custom_processor(df, title_row, data_row)
                    if processed_data:
                        for key, value in processed_data.items():
                            if key not in all_data:
                                all_data[key] = []

                            # 如果是學校名稱欄位，進行名稱轉換
                            if key == "學校名稱" and value is not None:
                                value = 轉換學校名稱(value)

                            all_data[key].append(value)
            else:
                # 使用默認處理邏輯，處理從資料開始列到結束列的每一列
                total_rows = current_end_row_index - data_row_index
                print(f"   📊 開始處理 {total_rows} 行資料...")
                processed_rows = 0
                filtered_rows = 0

                for row_idx in range(data_row_index, current_end_row_index):
                    data_row = df.iloc[row_idx].tolist()

                    # 如果啟用數據過濾，檢查學年度和學校名稱
                    if filter_data:
                        # 尋找學年度和學校名稱欄位的索引
                        year_col_idx = None
                        school_col_idx = None

                        for i, title in enumerate(title_row):
                            if pd.notna(title):
                                title_str = str(title).strip()
                                if '學年度' in title_str or '年度' in title_str:
                                    year_col_idx = i
                                elif '學校名稱' in title_str:
                                    school_col_idx = i

                        # 檢查學年度是否在有效範圍內
                        if year_col_idx is not None:
                            year_value = data_row[year_col_idx] if year_col_idx < len(data_row) else None
                            if not 是否為有效學年度(year_value):
                                filtered_rows += 1
                                continue  # 跳過此行

                        # 檢查是否為目標學校
                        if school_col_idx is not None:
                            school_value = data_row[school_col_idx] if school_col_idx < len(data_row) else None
                            if not 是否為目標學校(school_value):
                                filtered_rows += 1
                                continue  # 跳過此行

                    # 處理標題和對應的資料值
                    for i in range(len(title_row)):
                        if pd.notna(title_row[i]):  # 確保標題不是NaN
                            title = str(title_row[i]).strip()
                            if title not in all_data:
                                all_data[title] = []

                            # 添加值到對應的標題下
                            value = data_row[i] if i < len(data_row) and pd.notna(data_row[i]) else None

                            # 移除千分位逗號
                            if value is not None:
                                value = 移除千分位逗號(value)

                            # 如果是學校名稱欄位，進行名稱轉換
                            if title == "學校名稱" and value is not None:
                                value = 轉換學校名稱(value)

                            all_data[title].append(value)

                    processed_rows += 1

                # 顯示處理統計
                print(f"   ✅ 處理完成: 共處理 {processed_rows} 行，過濾掉 {filtered_rows} 行")

        except Exception as e:
            print(f"處理檔案 {file_path.name} 時發生錯誤: {e}")

    # 顯示處理了哪些檔案
    if processed_files:
        print(f"已處理檔案: {', '.join(processed_files)}")

    return all_data

def 建立整合CSV(data, output_dir, output_filename):
    """
    將整合後的數據保存為CSV檔案

    Args:
        data: 整合後的數據字典
        output_dir: 輸出檔案的目錄
        output_filename: 輸出檔案名稱(完整名稱，包含副檔名)

    Returns:
        保存成功返回True，否則返回False
    """
    if not data:
        print("沒有找到有效數據可整合")
        return False

    try:
        # 確保所有列有相同長度
        max_length = max(len(values) for values in data.values())
        for key in data:
            while len(data[key]) < max_length:
                data[key].append(None)

        # 創建DataFrame
        result_df = pd.DataFrame(data)

        # 如果有學校名稱欄位，確保所有學校名稱都已轉換為簡稱
        if "學校名稱" in result_df.columns:
            result_df["學校名稱"] = result_df["學校名稱"].apply(轉換學校名稱)

        # 生成輸出檔案路徑 (CSV格式)
        # 從原始檔名中移除副檔名，然後添加.csv
        csv_filename = Path(output_filename).stem + ".csv"
        output_path_csv = Path(output_dir) / csv_filename

        # 保存為CSV (使用UTF-8編碼確保中文顯示正常)
        result_df.to_csv(output_path_csv, index=False, encoding='utf-8-sig')

        print(f"✅ 成功生成整合檔案: {csv_filename} (CSV格式)")
        return True
    except Exception as e:
        print(f"❌ 保存檔案時發生錯誤: {e}")
        return False

def 處理檔案組(folder_path, file_config):
    """
    處理單一資料夾中指定類型的Excel檔案組

    Args:
        folder_path: 資料夾路徑
        file_config: 檔案處理配置字典，包含:
            {
                'file_keywords': 檔案關鍵字列表,
                'title_row_index': 標題列索引,
                'data_row_index': 資料開始列索引,
                'data_end_row_index': 資料結束列索引(可選，默認自動尋找"統計說明"),
                'separate_by_keyword': 是否按關鍵字分別整合(默認為True),
                'custom_processor': 自定義數據處理函數(可選)
            }

    Returns:
        (處理是否成功, 輸出檔案路徑列表) 的元組
    """
    # 獲取配置
    file_keywords = file_config.get('file_keywords', [])
    title_row_index = file_config.get('title_row_index', 2)  # 默認使用第三列為標題（索引從0開始）
    data_row_index = file_config.get('data_row_index', 3)  # 默認使用第四列為資料值開始（索引從0開始）
    data_end_row_index = file_config.get('data_end_row_index', None)  # 默認自動尋找"統計說明"
    separate_by_keyword = file_config.get('separate_by_keyword', True)  # 默認按關鍵字分別整合
    custom_processor = file_config.get('custom_processor')

    # 尋找符合條件的Excel檔案
    excel_files = 尋找檔案(folder_path, file_keywords, ['.xlsx', '.xls'])

    if not excel_files:
        print(f"在 {folder_path.name} 資料夾中未找到包含關鍵字 '{file_keywords}' 的Excel檔案")
        return False, []

    print(f"在 {folder_path.name} 資料夾中找到 {len(excel_files)} 個符合關鍵字的Excel檔案")

    output_files = []
    success_overall = True

    if separate_by_keyword and len(file_keywords) > 0:
        # 按照關鍵字分別處理檔案
        for keyword in file_keywords:
            keyword_files = [f for f in excel_files if keyword in f.name]

            if not keyword_files:
                continue

            print(f"處理關鍵字 '{keyword}' 的檔案...")

            # 處理該關鍵字的所有Excel檔案
            integrated_data = 處理Excel檔案(
                keyword_files,
                title_row_index,     # 標題列索引
                data_row_index,      # 資料開始列索引
                data_end_row_index,  # 資料結束列索引（自動尋找"統計說明"）
                custom_processor,
                filter_data=True     # 啟用智能數據過濾
            )

            if not integrated_data:
                print(f"未能從關鍵字 '{keyword}' 的檔案中提取有效數據")
                success_overall = False
                continue

            # 使用關鍵字生成輸出檔案名
            keyword_output_filename = f"已整合-{keyword}.xlsx"

            # 建立整合CSV
            success = 建立整合CSV(integrated_data, folder_path, keyword_output_filename)
            if success:
                # 添加xlsx格式檔案路徑
                output_files.append(Path(folder_path) / keyword_output_filename)
                # 添加csv格式檔案路徑
                csv_filename = f"已整合-{keyword}.csv"
                output_files.append(Path(folder_path) / csv_filename)
            else:
                success_overall = False
    else:
        # 不按關鍵字分別整合，而是整合所有檔案
        print(f"整合所有符合關鍵字的檔案...")

        integrated_data = 處理Excel檔案(
            excel_files,
            title_row_index,     # 標題列索引
            data_row_index,      # 資料開始列索引
            data_end_row_index,  # 資料結束列索引（自動尋找"統計說明"）
            custom_processor,
            filter_data=True     # 啟用智能數據過濾
        )

        if not integrated_data:
            print(f"未能從 {folder_path.name} 資料夾中的檔案提取有效數據")
            return False, []

        # 使用所有關鍵字組合生成檔案名
        combined_keywords = "-".join(file_keywords) if file_keywords else "data"
        output_filename = f"已整合-{combined_keywords}.xlsx"

        # 建立整合CSV
        success = 建立整合CSV(integrated_data, folder_path, output_filename)
        if success:
            # 添加xlsx格式檔案路徑
            output_files.append(Path(folder_path) / output_filename)
            # 添加csv格式檔案路徑
            csv_filename = f"已整合-{combined_keywords}.csv"
            output_files.append(Path(folder_path) / csv_filename)
        else:
            success_overall = False

    return success_overall, output_files

def 主程序(base_dir, folder_configs):
    """
    主程序 - 根據配置處理多個資料夾中的多組檔案

    Args:
        base_dir: 基礎目錄路徑
        folder_configs: 資料夾處理配置列表，每個配置為字典:
            {
                'folder_keyword': 資料夾關鍵字,
                'file_groups': 檔案組處理配置列表，每組為字典:
                    {
                        'file_keywords': 檔案關鍵字列表,
                        'title_row_index': 標題列索引,
                        'data_row_index': 資料開始列索引,
                        'data_end_row_index': 資料結束列索引(可選，默認自動尋找"統計說明"),
                        'separate_by_keyword': 是否按關鍵字分別整合(默認為True),
                        'custom_processor': 自定義處理函數(可選)
                    }
            }
    """
    base_path = Path(base_dir)
    if not base_path.exists() or not base_path.is_dir():
        print(f"錯誤: 目錄 {base_dir} 不存在或不是資料夾")
        return

    print(f"開始處理基礎目錄: {base_path}")

    # 處理每個資料夾配置
    for folder_config in folder_configs:
        folder_keyword = folder_config.get('folder_keyword')
        print(f"尋找關鍵字為 '{folder_keyword}' 的資料夾...")

        # 尋找符合關鍵字的資料夾
        target_folders = 尋找資料夾(base_path, [folder_keyword] if folder_keyword else None)

        if not target_folders:
            print(f"未找到關鍵字為 '{folder_keyword}' 的資料夾")
            continue

        for folder in target_folders:
            print(f"\n處理資料夾: {folder.name}")

            # 處理資料夾中的每組檔案
            file_groups = folder_config.get('file_groups', [])
            for file_group in file_groups:
                print(f"\n處理檔案組: {file_group.get('file_keywords', [])}")
                success, output_files = 處理檔案組(folder, file_group)
                if not success and not output_files:
                    print(f"處理檔案組失敗: {file_group.get('file_keywords', [])}")

    print("\n所有處理已完成")

# 主程式入口點
if __name__ == "__main__":
    # 基礎目錄
    base_directory = r"C:\Users\<USER>\Desktop\大專爬蟲"

    # 資料夾處理配置
    folder_configs = [
        # 學生
        {
            'folder_keyword': '學生', #資料夾
            'file_groups': [
                # 學生資料檔案組
                {
                    'file_keywords': ['在學學生數','外國學生數'], #檔案名關鍵字 
                    'title_row_index': 2,  # 第三列為標題（索引從0開始,Excel要減1）
                    'data_row_index': 3    # 第四列開始為資料值（索引從0開始,Excel要減1）
                    # 程式會自動尋找包含"統計說明"的列作為結束
                }
            ]
        },
        # # 教職
        # {
        #     'folder_keyword': '教職',
        #     'file_groups': [
        #         {
        #             'file_keywords': ['學年度學年度專任教師數','外籍專任教師數','日間生師比','編制外專任教師比率'],
        #             'title_row_index': 2,  # 第三列為標題
        #             'data_row_index': 3    # 第四列開始為資料值
        #         },
        #     ]
        # },
        # # 研究
        # {
        #     'folder_keyword': '研究',
        #     'file_groups': [
        #         {
        #             'file_keywords': ['產學合作計畫經費', '學校承接各單位資助計畫經費'],
        #             'title_row_index': 2,  # 第三列為標題
        #             'data_row_index': 3    # 第四列開始為資料值
        #         },
        #     ]
        # },
        # # 校務
        # {
        #     'folder_keyword': '校務',
        #     'file_groups': [
        #         {
        #             'file_keywords': ['圖書館', '學校提供一年級學生住宿之比率','學校開設全外語授課之院'],
        #             'title_row_index': 2,  # 第三列為標題
        #             'data_row_index': 3    # 第四列開始為資料值
        #         },
        #     ]
        # },
        # # 財務
        # {
        #     'folder_keyword': '財務',
        #     'file_groups': [
        #         {
        #             'file_keywords': ['國立學校可用資金', '學雜費收入占總收入之比率'],
        #             'title_row_index': 2,  # 第三列為標題
        #             'data_row_index': 3    # 第四列開始為資料值
        #         },
        #     ]
        # },
    ]

    # 執行主程序（舊版模式 + 智能數據過濾）
    print("🚀 資料清理程序")
    print("="*50)
    print("🔄 使用高度可配置模式 + 智能數據過濾")
    print("📊 數據過濾條件:")
    current_minguo = 獲取當前民國年()
    print(f"  📅 學年度範圍: 110 - {current_minguo}")
    print(f"  🏫 目標學校: 國立雲林科技大學、國立高雄科技大學、國立臺灣科技大學、國立臺北科技大學")
    print(f"  🔢 自動移除千分位逗號")
    print(f"  � 輸出格式: CSV檔案")
    print("="*50)

    主程序(base_directory, folder_configs)