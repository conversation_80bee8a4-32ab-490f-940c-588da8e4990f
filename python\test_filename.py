#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試檔名修改功能
"""

import re

def test_filename_generation():
    """測試檔名生成邏輯"""
    print("🧪 測試檔名生成功能")
    print("="*50)
    
    # 測試數據
    test_cases = [
        {
            'item_name': '在學學生數',
            'target_text': '學1-1.正式學籍在學學生人數-以「系(所)」統計',
            'expected': '在學學生數.xlsx'
        },
        {
            'item_name': '外國學生數',
            'target_text': '學3-2.外國學生數及其在學比率-以「系(所)」統計',
            'expected': '外國學生數.xlsx'
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n測試案例 {i}:")
        print(f"  📝 item_name: {case['item_name']}")
        print(f"  📄 target_text: {case['target_text']}")
        
        # 原本的邏輯（使用 target_text）
        old_safe_filename = re.sub(r'[<>:"/\\|?*]', '_', case['target_text'])
        old_output_filename = f"{old_safe_filename}.xlsx"
        
        # 新的邏輯（使用 item_name）
        new_safe_filename = re.sub(r'[<>:"/\\|?*]', '_', case['item_name'])
        new_output_filename = f"{new_safe_filename}.xlsx"
        
        print(f"  🗂️  舊檔名: {old_output_filename}")
        print(f"  ✨ 新檔名: {new_output_filename}")
        print(f"  🎯 預期檔名: {case['expected']}")
        
        if new_output_filename == case['expected']:
            print(f"  ✅ 測試通過")
        else:
            print(f"  ❌ 測試失敗")

def show_download_items():
    """顯示當前配置的下載項目"""
    print("\n📋 當前配置的下載項目:")
    print("="*50)
    
    # 從大專院校爬蟲.py導入配置
    try:
        from 大專院校爬蟲 import category_pages
        
        for category, config in category_pages.items():
            print(f"\n📂 {category}:")
            download_items = config.get('download_items', {})
            if download_items:
                for item_name, target_text in download_items.items():
                    safe_filename = re.sub(r'[<>:"/\\|?*]', '_', item_name)
                    output_filename = f"{safe_filename}.xlsx"
                    print(f"  • {item_name} -> {output_filename}")
            else:
                print(f"  ⚠️  沒有配置下載項目")
                
    except ImportError as e:
        print(f"❌ 無法導入配置: {e}")

if __name__ == "__main__":
    # 測試檔名生成
    test_filename_generation()
    
    # 顯示當前配置
    show_download_items()
    
    print(f"\n🎉 檔名測試完成！")
    print(f"💡 現在下載的檔案會使用 download_items 的 key 作為檔名")
