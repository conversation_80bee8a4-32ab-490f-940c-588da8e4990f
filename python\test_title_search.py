#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試title屬性搜索功能
"""

import requests
from bs4 import BeautifulSoup

# 設定基本資料
base_url = 'https://udb.moe.edu.tw'

def 獲取分類頁面內容(category_url, headers):
    """獲取分類頁面的HTML內容"""
    try:
        response = requests.get(category_url, headers=headers)
        response.encoding = 'utf-8'
        soup = BeautifulSoup(response.text, 'html.parser')
        return soup
    except Exception as e:
        print(f"獲取頁面內容時發生錯誤: {str(e)}")
        return None

def 尋找XLSX下載連結_新版(soup, target_text):
    """新版：使用title屬性搜索"""
    try:
        # 尋找包含目標文字的<a>標籤（使用title屬性搜索）
        target_links = soup.find_all('a', title=lambda title: title and target_text in title)
        
        if not target_links:
            print(f"找不到title包含 '{target_text}' 的連結")
            return None
        else:
            print(f"使用title屬性搜索找到 {len(target_links)} 個連結")
        
        # 找到目標連結後，尋找同一行的XLSX下載按鈕
        for i, link in enumerate(target_links, 1):
            print(f"  檢查第 {i} 個連結:")
            print(f"    title: {link.get('title', '')}")
            print(f"    href: {link.get('href', '')}")
            
            # 找到包含此連結的<tr>元素
            tr_element = link.find_parent('tr')
            if tr_element:
                # 在這個<tr>中尋找XLSX下載連結
                xlsx_link = tr_element.find('a', href=lambda href: href and '.xlsx' in href)
                if xlsx_link:
                    xlsx_url = xlsx_link.get('href')
                    # 如果是相對路徑，轉換為絕對路徑
                    if xlsx_url.startswith('/'):
                        xlsx_url = base_url + xlsx_url
                    print(f"    ✅ 找到XLSX連結: {xlsx_url}")
                    return xlsx_url
                else:
                    print(f"    ❌ 找不到XLSX下載連結")
            else:
                print(f"    ❌ 找不到父級<tr>元素")
        
        return None
        
    except Exception as e:
        print(f"尋找XLSX下載連結時發生錯誤: {str(e)}")
        return None

def 尋找XLSX下載連結_舊版(soup, target_text):
    """舊版：使用文字內容搜索"""
    try:
        # 尋找包含目標文字的<a>標籤
        target_links = soup.find_all('a', string=lambda text: text and target_text in text.strip())
        
        if not target_links:
            print(f"找不到包含 '{target_text}' 的連結")
            return None
        else:
            print(f"使用文字內容搜索找到 {len(target_links)} 個連結")
        
        # 找到目標連結後，尋找同一行的XLSX下載按鈕
        for link in target_links:
            # 找到包含此連結的<tr>元素
            tr_element = link.find_parent('tr')
            if tr_element:
                # 在這個<tr>中尋找XLSX下載連結
                xlsx_link = tr_element.find('a', href=lambda href: href and '.xlsx' in href)
                if xlsx_link:
                    xlsx_url = xlsx_link.get('href')
                    # 如果是相對路徑，轉換為絕對路徑
                    if xlsx_url.startswith('/'):
                        xlsx_url = base_url + xlsx_url
                    return xlsx_url
        
        return None
        
    except Exception as e:
        print(f"尋找XLSX下載連結時發生錯誤: {str(e)}")
        return None

def test_search_methods():
    """測試不同的搜索方法"""
    print("🧪 測試title屬性搜索功能")
    print("="*60)
    
    # 測試參數
    test_cases = [
        {
            'category': '教職類',
            'url': 'https://udb.moe.edu.tw/udata/DetailReportList/教職類',
            'target': '教11.編制外專任教師數及其比率-以「校」統計(112學年度起)'
        },
        {
            'category': '學生類',
            'url': 'https://udb.moe.edu.tw/udata/DetailReportList/學生類',
            'target': '學1-1.正式學籍在學學生人數-以「系(所)」統計'
        }
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    for case in test_cases:
        print(f"\n📂 測試分類: {case['category']}")
        print(f"🎯 目標文字: {case['target']}")
        print(f"🔗 頁面URL: {case['url']}")
        
        # 獲取頁面內容
        soup = 獲取分類頁面內容(case['url'], headers)
        if not soup:
            print("❌ 無法獲取頁面內容")
            continue
        
        print("✅ 成功獲取頁面內容")
        
        # 測試新版搜索（title屬性）
        print(f"\n🔍 測試新版搜索（title屬性）:")
        result_new = 尋找XLSX下載連結_新版(soup, case['target'])
        
        # 測試舊版搜索（文字內容）
        print(f"\n🔍 測試舊版搜索（文字內容）:")
        result_old = 尋找XLSX下載連結_舊版(soup, case['target'])
        
        # 比較結果
        print(f"\n📊 結果比較:")
        print(f"  新版結果: {'✅ 成功' if result_new else '❌ 失敗'}")
        print(f"  舊版結果: {'✅ 成功' if result_old else '❌ 失敗'}")
        
        if result_new:
            print(f"  新版連結: {result_new}")
        if result_old:
            print(f"  舊版連結: {result_old}")
        
        print("-" * 60)

if __name__ == "__main__":
    test_search_methods()
