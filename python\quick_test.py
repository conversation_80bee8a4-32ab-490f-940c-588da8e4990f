#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速測試title搜索功能
"""

from 大專院校爬蟲 import 獲取分類頁面內容, 尋找XLSX下載連結

def quick_test():
    """快速測試"""
    print("🧪 快速測試title搜索功能")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    # 測試教職類的編制外專任教師比率
    category_url = 'https://udb.moe.edu.tw/udata/DetailReportList/教職類'
    target_text = '教11.編制外專任教師數及其比率-以「校」統計(112學年度起)'
    
    print(f"📂 測試頁面: {category_url}")
    print(f"🎯 目標文字: {target_text}")
    
    # 獲取頁面內容
    soup = 獲取分類頁面內容(category_url, headers)
    if soup:
        print("✅ 成功獲取頁面內容")
        
        # 尋找XLSX下載連結
        xlsx_url = 尋找XLSX下載連結(soup, target_text)
        if xlsx_url:
            print(f"✅ 成功找到XLSX下載連結: {xlsx_url}")
        else:
            print("❌ 找不到XLSX下載連結")
    else:
        print("❌ 無法獲取頁面內容")

if __name__ == "__main__":
    quick_test()
