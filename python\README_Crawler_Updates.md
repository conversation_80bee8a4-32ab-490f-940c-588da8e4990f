# 大專院校爬蟲更新說明

## 概述

已成功重新設計 `大專院校爬蟲.py` 的整體架構，現在可以：

1. **爬取5個不同分類的頁面**
2. **自動尋找並下載指定的CSV文件**
3. **支持批量下載邏輯**
4. **不再需要下載多個學校的資料（因為CSV中已包含所有學校）**

## 主要架構變更

### 1. 新的頁面配置結構

```python
category_pages = {
    '學生類': {
        'url': 'https://udb.moe.edu.tw/udata/DetailReportList/學生類',
        'download_items': {
            '在學學生數': '學1-1.正式學籍在學學生人數-以「系(所)」統計',
            '外國學生數': '學3-2.外國學生數及其在學比率-以「系(所)」統計',
        }
    },
    '教職類': {
        'url': 'https://udb.moe.edu.tw/udata/DetailReportList/教職類',
        'download_items': {}
    },
    # ... 其他分類
}
```

### 2. 核心功能函數

#### `獲取分類頁面內容(category_url, headers)`
- 獲取指定分類頁面的HTML內容
- 返回BeautifulSoup對象

#### `尋找CSV下載連結(soup, target_text)`
- 在頁面中尋找包含指定文字的連結
- 自動找到對應的CSV下載按鈕
- 返回CSV下載URL

#### `下載CSV檔案(csv_url, headers, output_path)`
- 下載CSV文件到指定路徑
- 包含重試機制
- 自動創建目錄結構

#### `處理單一下載任務(task_info)`
- 處理單個CSV下載任務
- 支持多線程並行處理
- 包含完整的錯誤處理

## 工作流程

### 1. 頁面爬取流程
```
1. 訪問分類頁面 (如：學生類)
2. 解析HTML內容
3. 尋找目標文字的連結
4. 定位同一行的CSV下載按鈕
5. 獲取CSV下載URL
6. 下載CSV文件
```

### 2. HTML結構解析

目標HTML結構：
```html
<tr>
    <td>
        <a href="..." title="學1-1.正式學籍在學學生人數-以「系(所)」統計">
            學1-1.正式學籍在學學生人數-以「系(所)」統計
        </a>
    </td>
    <td headers="csv" class="text-center">
        <a class="btn btn-warning" href="/download/udata/static_file/學1-1.正式學籍在學學生人數-以「系(所)」統計.csv">
            <i class="glyphicon glyphicon-download-alt"></i> .csv
        </a>
    </td>
</tr>
```

### 3. 文件組織結構

```
桌面/大專爬蟲/
├── 學生類/
│   ├── 學1-1.正式學籍在學學生人數-以「系(所)」統計.csv
│   └── 學3-2.外國學生數及其在學比率-以「系(所)」統計.csv
├── 教職類/
├── 研究類/
├── 校務類/
└── 財務類/
```

## 配置說明

### 添加新的下載項目

在 `category_pages` 中添加新的下載項目：

```python
'學生類': {
    'url': 'https://udb.moe.edu.tw/udata/DetailReportList/學生類',
    'download_items': {
        '在學學生數': '學1-1.正式學籍在學學生人數-以「系(所)」統計',
        '外國學生數': '學3-2.外國學生數及其在學比率-以「系(所)」統計',
        # 添加新項目
        '新項目名稱': '頁面中的完整文字',
    }
}
```

### 關鍵字對應

根據您的要求，關鍵字對應如下：

```python
'在學學生數': '學1-1.正式學籍在學學生人數-以「系(所)」統計'
'外國學生數': '學3-2.外國學生數及其在學比率-以「系(所)」統計'
```

## 使用方式

### 基本使用
```python
from 大專院校爬蟲 import 主程序

# 執行完整下載
results = 主程序()
```

### 測試功能
```python
python test_crawler.py
```

### 單獨測試某個功能
```python
from 大專院校爬蟲 import 獲取分類頁面內容, 尋找CSV下載連結

headers = {'User-Agent': 'Mozilla/5.0...'}
soup = 獲取分類頁面內容('https://udb.moe.edu.tw/udata/DetailReportList/學生類', headers)
csv_url = 尋找CSV下載連結(soup, '學1-1.正式學籍在學學生人數-以「系(所)」統計')
```

## 特色功能

### 1. 智能連結尋找
- 自動在頁面中尋找包含目標文字的連結
- 自動定位同一行的CSV下載按鈕
- 支持相對路徑轉絕對路徑

### 2. 批量下載
- 支持多線程並行下載
- 可配置最大並行數 (`MAX_WORKERS`)
- 包含進度顯示

### 3. 錯誤處理與重試
- 每個步驟都有完整的錯誤處理
- 支持自動重試機制
- 詳細的錯誤報告

### 4. 靈活配置
- 易於添加新的分類和下載項目
- 支持自定義請求標頭
- 可調整請求間隔

## 注意事項

1. **目標文字必須完全匹配**：確保 `target_text` 與頁面中的文字完全一致
2. **網路穩定性**：建議在穩定的網路環境下執行
3. **請求頻率**：已設置請求間隔避免對服務器造成壓力
4. **文件覆蓋**：相同名稱的文件會被覆蓋

## 擴展性

### 添加新分類
```python
category_pages['新分類'] = {
    'url': 'https://udb.moe.edu.tw/udata/DetailReportList/新分類',
    'download_items': {
        '項目名稱': '頁面中的目標文字'
    }
}
```

### 自定義下載邏輯
可以繼承或修改 `處理單一下載任務` 函數來實現自定義的下載邏輯。

## 優勢

1. **簡化架構**：不再需要處理複雜的學校參數和AJAX請求
2. **直接下載**：直接從靜態文件連結下載CSV
3. **完整數據**：每個CSV文件包含所有學校的數據
4. **易於維護**：清晰的配置結構，易於添加新項目
5. **高效率**：並行下載，支持進度顯示
