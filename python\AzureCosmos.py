import os
import csv
import pandas as pd
from azure.cosmos import CosmosClient, PartitionKey, exceptions
import uuid
from typing import Dict, Optional, List
from pathlib import Path

class AzureCosmosManager:
    """Azure Cosmos DB 管理類別"""

    def __init__(self, endpoint: str, key: str, database_name: str):
        """
        初始化 Azure Cosmos DB 管理器

        Args:
            endpoint: Azure Cosmos DB 端點
            key: 存取金鑰
            database_name: 資料庫名稱
        """
        self.endpoint = endpoint
        self.key = key
        self.database_name = database_name
        self.client = CosmosClient(endpoint, key)
        self.database = None
        self.containers = {}  # 儲存已建立的容器參考

        # 定義所有集合的配置
        self.container_configs = {
            # # 研究類
            # "Res_CombineMoney": {
            #     "name": "Res_CombineMoney",
            #     "partition_key": "/年度",
            #     "csv_file": "../研究/已整合-產學合作計畫經費.csv",
            #     "year_column": "年度",  # CSV 中年度欄位的名稱
            #     # 在 Cosmos DB 中儲存的資料欄位名稱 => data
            # },
            # "Res_PlanMoney": {
            #     "name": "Res_PlanMoney",
            #     "partition_key": "/年度",
            #     "csv_file": "../研究/已整合-學校承接各單位資助計畫經費.csv",
            #     "year_column": "年度",
            # },
            # 校務類
            "UA_Lib": {
                "name": "UA_Lib",
                "partition_key": "/年度",
                "csv_file": "../校務/已整合-圖書館歷年版.csv",
                "year_column": "學年度",
            },
            # "UA_Lib": {
            #     "name": "UA_Lib",
            #     "partition_key": "/年度",
            #     "csv_file": "../校務/已整合-圖書館.csv",
            #     "year_column": "學年度",
            # },
            # "UA_1stDoomRate": {
            #     "name": "UA_1stDoomRate",
            #     "partition_key": "/年度",
            #     "csv_file": "../校務/已整合-學校提供一年級學生住宿之比率.csv",
            #     "year_column": "學年度",
            # }
            # ,
            # "UA_ForeignCollege": {
            #     "name": "UA_ForeignCollege",
            #     "partition_key": "/年度",
            #     "csv_file": "../校務/已整合-學校開設全外語授課之院.csv",
            #     "year_column": "學年度",
            # },
            # # 財務類
            # "Final_SchoolFunds": {
            #     "name": "Final_SchoolFunds",
            #     "partition_key": "/年度",
            #     "csv_file": "../財務/已整合-國立學校可用資金.csv",
            #     "year_column": "年度",
            # },
            # "Final_TRRatio": {
            #     "name": "Final_TRRatio",
            #     "partition_key": "/年度",
            #     "csv_file": "../財務/已整合-學雜費收入占總收入之比率.csv",
            #     "year_column": "年度",
            # },
            # # 教職類
            # "Teach_D-STR": {
            #     "name": "Teach_D-STR",
            #     "partition_key": "/年度",
            #     "csv_file": "../教職/已整合-日間生師比.csv",
            #     "year_column": "學年度",
            # },
            # "Teach_TeacherOutRatio": {
            #     "name": "Teach_TeacherOutRatio",
            #     "partition_key": "/年度",
            #     "csv_file": "../教職/已整合-編制外專任教師比率.csv",
            #     "year_column": "學年度",
            # },
            # "Teach_ForeignTeacherTotal": {
            #     "name": "Teach_ForeignTeacherTotal",
            #     "partition_key": "/年度",
            #     "csv_file": "../教職/已整合-外籍專任教師數.csv",
            #     "year_column": "學年度",
            # },
            # # 學生類
            # "Stud_ForeignStudTotal": {
            #     "name": "Stud_ForeignStudTotal",
            #     "partition_key": "/年度",
            #     "csv_file": "../學生/已整合-在學學生數.csv",
            #     "year_column": "學年度",
            # },
            # "Stud_StudTotal": {
            #     "name": "Stud_StudTotal",
            #     "partition_key": "/年度",
            #     "csv_file": "../學生/已整合-外國學生數.csv",
            #     "year_column": "學年度",
            # },
        }

    def connect_to_database(self) -> bool:
        """
        連接到資料庫

        Returns:
            bool: 連接成功返回 True，失敗返回 False
        """
        try:
            self.database = self.client.get_database_client(self.database_name)
            print(f"✅ 資料庫 '{self.database_name}' 連接成功")
            return True
        except exceptions.CosmosHttpResponseError as e:
            print(f"❌ 連接資料庫時發生錯誤: {e.message}")
            return False

    def create_container(self, container_key: str) -> Optional[object]:
        """
        建立或取得指定的容器

        Args:
            container_key: 容器配置的鍵值

        Returns:
            容器物件或 None
        """
        if container_key not in self.container_configs:
            print(f"❌ 未找到容器配置: {container_key}")
            return None

        config = self.container_configs[container_key]
        container_name = config["name"]
        partition_key_path = PartitionKey(path=config["partition_key"])

        try:
            container = self.database.create_container(
                id=container_name,
                partition_key=partition_key_path
            )
            print(f"✅ 容器 '{container_name}' 建立成功")
        except exceptions.CosmosResourceExistsError:
            container = self.database.get_container_client(container_name)
            print(f"ℹ️  容器 '{container_name}' 已存在")
        except exceptions.CosmosHttpResponseError as e:
            print(f"❌ 建立容器 '{container_name}' 時發生錯誤: {e.message}")
            return None

        self.containers[container_key] = container
        return container

    def create_all_containers(self) -> Dict[str, object]:
        """
        建立所有配置的容器

        Returns:
            建立成功的容器字典
        """
        created_containers = {}

        for container_key in self.container_configs:
            container = self.create_container(container_key)
            if container:
                created_containers[container_key] = container

        return created_containers

    def read_csv_file(self, file_path: str) -> Optional[pd.DataFrame]:
        """
        讀取 CSV 檔案

        Args:
            file_path: CSV 檔案路徑

        Returns:
            pandas DataFrame 或 None
        """
        try:
            # 嘗試不同的編碼格式
            encodings = ['utf-8', 'utf-8-sig', 'big5', 'gbk', 'cp950']

            for encoding in encodings:
                try:
                    df = pd.read_csv(file_path, encoding=encoding)
                    print(f"✅ 成功讀取 CSV 檔案: {file_path} (編碼: {encoding})")
                    print(f"📊 資料筆數: {len(df)} 筆")
                    return df
                except UnicodeDecodeError:
                    continue

            print(f"❌ 無法讀取 CSV 檔案，嘗試的編碼格式都失敗: {file_path}")
            return None

        except FileNotFoundError:
            print(f"❌ 找不到 CSV 檔案: {file_path}")
            return None
        except Exception as e:
            print(f"❌ 讀取 CSV 檔案時發生錯誤: {e}")
            return None

    def get_year_from_dataframe(self, df: pd.DataFrame, config: Dict) -> str:
        """
        從 DataFrame 中取得年度值（保留向後兼容性）

        Args:
            df: pandas DataFrame
            config: 容器配置

        Returns:
            年度字串
        """
        year_column = config.get("year_column", "年度")

        if year_column in df.columns and len(df) > 0:
            # 取第一筆非空值
            for _, row in df.iterrows():
                year_value = row[year_column]
                if pd.notna(year_value):
                    return str(year_value)

        # 如果找不到年度欄位或欄位為空，使用 "Unknown"
        print(f"⚠️  未找到年度欄位 '{year_column}' 或欄位為空，使用預設值: Unknown")
        return "Unknown"

    def import_csv_data(self, container_key: str) -> bool:
        """
        從 CSV 檔案匯入資料到指定容器，依照年度分別處理

        Args:
            container_key: 容器配置的鍵值

        Returns:
            bool: 匯入成功返回 True
        """
        if container_key not in self.containers:
            print(f"❌ 容器 '{container_key}' 尚未建立")
            return False

        config = self.container_configs[container_key]

        # 檢查是否有指定 CSV 檔案
        if "csv_file" not in config:
            print(f"❌ 容器 '{container_key}' 未指定 CSV 檔案")
            return False

        csv_path = config["csv_file"]

        # 讀取 CSV 檔案
        df = self.read_csv_file(csv_path)
        if df is None:
            return False

        container = self.containers[container_key]
        year_column = config.get("year_column", "年度")

        try:
            # 1. 按年度分組資料
            if year_column not in df.columns:
                print(f"❌ CSV 檔案中找不到年度欄位: {year_column}")
                return False

            # 按年度分組
            grouped_data = df.groupby(year_column)

            success_count = 0
            total_years = len(grouped_data)

            print(f"📊 發現 {total_years} 個不同年度的資料")

            for year, year_df in grouped_data:
                year_str = str(year)
                print(f"\n🔄 處理年度: {year_str} ({len(year_df)} 筆資料)")

                # 2. 檢查該年度是否已存在於 Azure Cosmos DB
                existing_item = self.check_existing_year_data(container, year_str)

                # 3. 將該年度的 DataFrame 轉換為記錄列表
                records = []
                for index, row in year_df.iterrows():
                    record = {}
                    for column in year_df.columns:
                        value = row[column]
                        # 處理 NaN 值
                        if pd.isna(value):
                            record[column] = None
                        else:
                            record[column] = value
                    records.append(record)

                # 4. 建立或更新資料結構
                if existing_item:
                    # 覆蓋現有資料
                    print(f"🔍 年度 {year_str} 已存在，將覆蓋現有資料")
                    data = {
                        "id": existing_item["id"],  # 使用現有的 ID
                        "年度": year_str,
                        "data": records
                    }
                    # 使用 upsert 來更新資料
                    container.upsert_item(body=data)
                    print(f"✅ 年度 {year_str} 資料已更新")
                else:
                    # 新增新資料
                    print(f"🔍 年度 {year_str} 不存在，將新增資料")
                    data = {
                        "id": str(uuid.uuid4()),  # 新的唯一ID
                        "年度": year_str,
                        "data": records
                    }
                    container.create_item(body=data)
                    print(f"✅ 年度 {year_str} 資料已新增")

                success_count += 1

            print(f"\n🎉 成功處理 {success_count}/{total_years} 個年度的資料")
            return success_count == total_years

        except exceptions.CosmosHttpResponseError as e:
            print(f"❌ 匯入資料時發生錯誤: {e.message}")
            return False
        except Exception as e:
            print(f"❌ 處理資料時發生錯誤: {e}")
            return False

    def check_existing_year_data(self, container, year: str) -> Optional[Dict]:
        """
        檢查指定年度的資料是否已存在於容器中

        Args:
            container: Cosmos DB 容器
            year: 年度字串

        Returns:
            如果存在則返回該項目，否則返回 None
        """
        try:
            query = f"SELECT * FROM c WHERE c.年度 = '{year}'"
            items = list(container.query_items(
                query=query,
                enable_cross_partition_query=True
            ))

            if items:
                return items[0]  # 返回第一筆匹配的資料
            else:
                return None

        except Exception as e:
            print(f"❌ 查詢年度 {year} 資料時發生錯誤: {e}")
            return None

    def query_data_by_year(self, container_key: str, year: str = None):
        """
        查詢指定年度的資料

        Args:
            container_key: 容器鍵值
            year: 年度，如果為 None 則查詢所有年度
        """
        if container_key not in self.containers:
            print(f"❌ 容器 '{container_key}' 尚未建立")
            return

        container = self.containers[container_key]

        try:
            if year:
                query = f"SELECT * FROM c WHERE c.年度 = '{year}'"
            else:
                query = "SELECT * FROM c"
                print("🔍 查詢所有年度的資料...")

            items = list(container.query_items(
                query=query,
                enable_cross_partition_query=True
            ))

        except Exception as e:
            print(f"❌ 查詢資料時發生錯誤: {e}")

    def list_all_years(self, container_key: str):
        """
        列出容器中所有的年度

        Args:
            container_key: 容器鍵值
        """
        if container_key not in self.containers:
            print(f"❌ 容器 '{container_key}' 尚未建立")
            return

        container = self.containers[container_key]

        try:
            query = "SELECT DISTINCT c.年度 FROM c"
            items = list(container.query_items(
                query=query,
                enable_cross_partition_query=True
            ))

            years = [item['年度'] for item in items]
            years.sort()

        except Exception as e:
            print(f"❌ 查詢年度列表時發生錯誤: {e}")

    def import_csv_data_for_all_containers(self) -> Dict[str, bool]:
        """
        為所有有 CSV 檔案配置的容器匯入資料

        Returns:
            各容器匯入結果的字典
        """
        results = {}

        for container_key, config in self.container_configs.items():
            if "csv_file" in config and container_key in self.containers:
                print(f"\n📥 處理容器: {config['name']}")
                results[container_key] = self.import_csv_data(container_key)
            else:
                if "csv_file" not in config:
                    print(f"ℹ️  容器 '{config['name']}' 未配置 CSV 檔案，跳過")
                if container_key not in self.containers:
                    print(f"⚠️  容器 '{container_key}' 尚未建立，跳過")

        return results

    def list_containers(self):
        """列出所有配置的容器資訊"""
        print("\n📋 容器配置清單:")
        print("-" * 100)
        for key, config in self.container_configs.items():
            csv_info = f"📄 CSV: {config.get('csv_file', '無')}" if 'csv_file' in config else "📄 CSV: 無"
            year_col = f"📅 CSV年度欄位: {config.get('year_column', '未設定')}"

            print(f"   {csv_info}")
            print(f"   {year_col}")
        print("-" * 100)

    def query_data(self, container_key: str, query: str = None, limit: int = 1):
        """
        查詢容器中的資料

        Args:
            container_key: 容器鍵值
            query: SQL 查詢語句，預設查詢第 1 筆
            limit: 限制筆數
        """
        if container_key not in self.containers:
            print(f"❌ 容器 '{container_key}' 尚未建立")
            return

        container = self.containers[container_key]
        config = self.container_configs[container_key]

        if query is None:
            query = f"SELECT TOP {limit} * FROM c"

        try:
            items = list(container.query_items(
                query=query,
                enable_cross_partition_query=True
            ))

            print(f"\n📊 查詢結果 ({len(items)} 筆):")
            print("-" * 60)
            for i, item in enumerate(items, 1):
                print(f"第 {i} 筆資料:")
                print(f"  ID: {item.get('id', 'N/A')}")
                print(f"  年度: {item.get('年度', 'N/A')}")

                # 固定使用 "data" 作為資料欄位名稱
                data_field_name = "data"
                if data_field_name in item:
                    data_list = item[data_field_name]
                    print(f"  {data_field_name}: 包含 {len(data_list)} 筆資料")
                    print(f"  前 3 筆資料:")
                    for j, record in enumerate(data_list[:3], 1):
                        # 顯示第一個非 ID 的欄位作為代表
                        display_field = None
                        for field_name in record.keys():
                            if field_name not in ['id', 'ID']:
                                display_field = field_name
                                break

                        if display_field:
                            print(f"    {j}. {display_field}: {record.get(display_field, 'N/A')}")
                        else:
                            print(f"    {j}. [資料記錄]")

                    if len(data_list) > 3:
                        print(f"    ... 還有 {len(data_list) - 3} 筆資料")

                print("-" * 40)

        except Exception as e:
            print(f"❌ 查詢資料時發生錯誤: {e}")


def main():
    """主程式"""
    # 連線資訊配置
    ENDPOINT = "https://yuntech-japan-nosql.documents.azure.com:443/"
    KEY = "****************************************************************************************"
    DATABASE_NAME = "Dashboard"

    # 建立 Cosmos DB 管理器
    cosmos_manager = AzureCosmosManager(ENDPOINT, KEY, DATABASE_NAME)

    try:
        # 1. 連接到資料庫
        if not cosmos_manager.connect_to_database():
            return

        # 2. 列出容器配置
        cosmos_manager.list_containers()

        # 3. 建立所有容器
        cosmos_manager.create_all_containers()

        # 4. 自動匯入所有 CSV 資料至 Azure CosmosDB
        print("\n" + "="*60)
        print("🚀 開始匯入 CSV 資料...")
        print("="*60)

        import_results = cosmos_manager.import_csv_data_for_all_containers()

        # 5. 顯示匯入結果
        for container_key, success in import_results.items():
            status = "✅ 成功" if success else "❌ 失敗"
            container_name = cosmos_manager.container_configs[container_key]["name"]
            print(f"  {container_name}: {status}")

        # 查詢 UA_Lib 容器的所有年度
        if "UA_Lib" in cosmos_manager.containers:
            cosmos_manager.list_all_years("UA_Lib")
            cosmos_manager.query_data_by_year("UA_Lib", "113")

        print("\n🎉 所有操作完成！")

    except exceptions.CosmosHttpResponseError as e:
        print(f"❌ Cosmos DB 操作錯誤: {e.message}")
    except Exception as e:
        print(f"❌ 發生未預期的錯誤: {e}")


if __name__ == "__main__":
    main()