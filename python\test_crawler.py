#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試大專院校爬蟲功能
"""

from 大專院校爬蟲 import 主程序, 獲取分類頁面內容, 尋找XLSX下載連結, category_pages

def test_single_page():
    """測試單一頁面的XLSX下載功能"""
    print("🧪 測試單一頁面功能...")

    # 測試學生類頁面
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }

    category_url = category_pages['學生類']['url']
    target_text = '學1-1.正式學籍在學學生人數-以「系(所)」統計'

    print(f"📂 測試頁面: {category_url}")
    print(f"🎯 尋找目標: {target_text}")

    # 獲取頁面內容
    soup = 獲取分類頁面內容(category_url, headers)
    if soup:
        print("✅ 成功獲取頁面內容")

        # 尋找XLSX下載連結
        xlsx_url = 尋找XLSX下載連結(soup, target_text)
        if xlsx_url:
            print(f"✅ 找到XLSX下載連結: {xlsx_url}")
        else:
            print("❌ 找不到XLSX下載連結")
    else:
        print("❌ 無法獲取頁面內容")

def test_all_categories():
    """測試所有分類的配置"""
    print("\n🧪 測試所有分類配置...")
    
    for category, config in category_pages.items():
        print(f"\n📂 分類: {category}")
        print(f"🔗 URL: {config['url']}")
        
        download_items = config['download_items']
        if download_items:
            print(f"📥 下載項目 ({len(download_items)} 個):")
            for item_name, target_text in download_items.items():
                print(f"  • {item_name}: {target_text}")
        else:
            print("⚠️  沒有配置下載項目")

def main():
    """主測試函數"""
    print("🚀 開始測試大專院校爬蟲功能")
    print("="*60)
    
    # 測試配置
    test_all_categories()
    
    # 測試單一頁面
    test_single_page()
    
    # 詢問是否執行完整下載
    print("\n" + "="*60)
    response = input("是否執行完整的XLSX下載測試？(y/N): ").strip().lower()

    if response in ['y', 'yes']:
        print("\n🚀 開始執行完整下載測試...")
        results = 主程序()

        print(f"\n📊 測試結果:")
        print(f"✅ 成功: {len(results['success'])} 個")
        print(f"❌ 失敗: {len(results['failed'])} 個")
    else:
        print("⏭️  跳過完整下載測試")
    
    print("\n🎉 測試完成！")

if __name__ == "__main__":
    main()
